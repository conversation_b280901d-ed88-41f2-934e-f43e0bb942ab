import { useEffect, useState } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import ReactRouterPrompt from 'react-router-prompt';

import ChatbotShortcuts from '@/components/Chatbot/ChatbotShortcuts';
import ChatManager from '@/components/Chatbot/ChatManager';
import DChatbotSidebar from '@/components/Chatbot/Details/ChatbotSidebar';
import Conversations from '@/components/Conversations';
import DLoading from '@/components/DLoading';
import VoiceConnectPopup from '@/components/Chatbot/VoiceConnectPopup';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import useDante<PERSON>pi from '@/hooks/useDanteApi';
import LayoutRightSidebar from '@/layouts/LayoutRightSidebar';
import * as chatbotService from '@/services/chatbot.service';
import { useChatbotStore } from '@/stores/chatbot/chatbotStore';
import useLayoutStore from '@/stores/layout/layoutStore';
import { useUserStore } from '@/stores/user/userStore';
import { useCustomizationStore } from '@/stores/customization/customizationStore';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import { Transition } from '@headlessui/react';
import clsx from 'clsx';
import DToastContainer from '@/components/DToast/DToastContainer';
import * as customizationService from '@/services/customization.service';
import CompletionPopup from '@/components/Chatbot/Create/CompletionPopup';

const ChatbotDetails = () => {
  let params = useParams();
  const location = useLocation();
  const isAboveMd = useIsAboveBreakpoint('md');
  const setLayoutTitle = useLayoutStore((state) => state.setLayoutTitle);
  const setSidebarOpen = useLayoutStore((state) => state.setSidebarOpen);
  const setSelectedChatbot = useChatbotStore(
    (state) => state.setSelectedChatbot
  );
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const setProgressBar = useLayoutStore((state) => state.setProgressBar);
  const setChatbotCustomization = useCustomizationStore(
    (state) => state.setChatbotCustomization
  );
  const { auth } = useUserStore((state) => state);

  const isAboveSm = useIsAboveBreakpoint('sm');

  const [showConversation, setShowConversation] = useState(false);


  const [customizationData, setCustomizationData] = useState(false);
  const [shouldRefetchConversations, setShouldRefetchConversations] = useState(false);

  // State for the voice connect popup

  const [isVoicePreviewPlaying, setIsVoicePreviewPlaying] = useState(false);

  const [showCompletionPopup, setShowCompletionPopup] = useState(false);

  const { data, isLoading } = useDanteApi(
    chatbotService.getChatbotInfoById,
    [],
    {},
    params.id
  );

  const {
    data: dataOverview,
    loading: loadingOverview,
    refetch: refetchOverview,
  } = useDanteApi(customizationService.getChatbotOverviewById, [], {}, params.id);

  const { data: customizationRawData, isLoading: isLoadingCustomization } =
    useDanteApi(customizationService.getChatbotCustomizationById, [], {}, {
      kb_id: params.id,
    });

  const forceRefetchConversations = () => {
    setShouldRefetchConversations(!shouldRefetchConversations);
  };

  useEffect(() => {
    setSidebarOpen(false);
    if (!isAboveMd) {
      setLayoutTitle('Chatbot');
      setShowConversation(true);
    } else {
      setLayoutTitle('');
    }
  }, [window.innerWidth]);

  useEffect(() => {
    setSelectedChatbot(data?.results);
  }, [data, setSelectedChatbot]);

  useEffect(() => {
    setProgressBar([]);
  }, []);

  // Check if we should show the completion popup
  useEffect(() => {
    // Check if we just came from chatbot creation
    const fromChatbotCreation = location.state?.fromChatbotCreation;

    if (fromChatbotCreation && data?.results) {
      setShowCompletionPopup(true);
      // Clear the state so the popup doesn't show again on refresh
      window.history.replaceState({}, document.title, location.pathname);
    }
  }, [location, data]);

  useEffect(() => {
    if (customizationRawData) {
      setCustomizationData({
        initial_messages: customizationRawData?.initial_messages,
        prompt_suggestions: customizationRawData?.prompt_suggestions,
        suggested_prompts_enabled:
          customizationRawData?.suggested_prompts_enabled,
        suggested_prompts_appearance:
          customizationRawData?.suggested_prompts_appearance,
        ...customizationRawData,
      });
    }
    setChatbotCustomization(customizationRawData);
  }, [customizationRawData]);

  // Create a function to handle the voice preview state changes
  const handleVoicePreviewStateChange = (isPlaying) => {
    setIsVoicePreviewPlaying(isPlaying);
  };

  if (isLoading) {
    return <DLoading show={isLoading} />;
  }

  return (
    <div className="flex flex-col w-full relative items-end h-full">
      <LayoutRightSidebar
        RightSidebar={(handleRightSidebar) => (
          <div className="w-full sm:w-[360px] 3xl:w-[440px] h-full overflow-y-auto overflow-x-hidden scrollbar">
            <div
              className={clsx(
                'h-full overflow-hidden scrollbar transition-all duration-200',
                showConversation ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-1 pointer-events-none'
              )}
            >
              {showConversation && (
                <Conversations
                  onClose={(type = 'close') => {
                    if (type === 'openConversation') {
                      !isAboveSm && setShowConversation(false);
                    } else {
                      setShowConversation(false);
                    }
                    handleRightSidebar();
                  }}
                  forceRefetch={forceRefetchConversations}
                  shouldRefetch={shouldRefetchConversations}
                />
              )}
            </div>
            <Transition show={!showConversation}>
              <div
                className={clsx(
                  'transition-all duration-100 h-full overflow-hidden scrollbar',
                  'data-[closed]:opacity-0 data-[closed]:translate-x-1',
                  'data-[enter]:delay-100 data-[leave]:delay-0'
                )}
              >
                <ChatbotShortcuts
                  customization={{
                    kb_id: params.id,
                    ...customizationData
                  }}
                  data={dataOverview}
                  loading={loadingOverview}
                  refetchOverview={refetchOverview}
                  onVoicePreviewStateChange={handleVoicePreviewStateChange}
                />
              </div>
            </Transition>
          </div>
        )}
      >
        {(handleRightSidebar) => {
          return (
            <div className="flex flex-col gap-size6 min-h-screen md:min-h-0 md:h-full">
              <div className="flex flex-col gap-size6 h-full min-h-[80vh] md:h-full bg-white rounded-size1 border border-grey-10">
                <div className="w-full h-full 3xl:max-w-[1200px] 3xl:mx-auto">
                <ChatManager
                  config={{
                    kb_id: params.id,
                    access_token: auth?.access_token,
                    chatbot_profile_pic: dataOverview?.icon,
                    ...customizationData,
                    name: dataOverview?.name,
                  }}
                  hiddenConversation={false}
                  isInApp={true}
                  showMenuBtn
                  hiddenPoweredByDante
                  handleOpenDanteConversations={() => {
                    setShowConversation(true);
                    handleRightSidebar();
                  }}
                  forceRefetch={forceRefetchConversations}
                />
                </div>
              </div>

              {!isAboveMd && (
                <ChatbotShortcuts
                  customization={{
                    kb_id: params.id,
                    ...customizationData
                  }}
                  data={dataOverview}
                  loading={loadingOverview}
                  refetchOverview={refetchOverview}
                />
              )}
            </div>
          );
        }}
      </LayoutRightSidebar>

      {/* Voice Connect Popup */}
      {/* <VoiceConnectPopup
        show={showVoiceConnectPopup}
        onClose={() => setShowVoiceConnectPopup(false)}
        chatbotId={params.id}
        chatbotName={data?.results?.knowledge_base?.knowledge_base_name || ''}
      /> */}

      {/* Completion Popup */}
      <CompletionPopup
        isOpen={showCompletionPopup}
        onClose={() => setShowCompletionPopup(false)}
        chatbotId={params.id}
      />

      {/* Voice confirmation prompt for navigation */}
      <ReactRouterPrompt when={isVoicePreviewPlaying}>
        {({ isActive, onConfirm, onCancel }) => (
          <DConfirmationModal
            open={isActive}
            onClose={onCancel}
            onConfirm={onConfirm}
            title="Stop AI Voice Agent?"
            description="Leaving this page will stop the AI Voice Agent that is currently speaking. Are you sure you want to leave?"
            confirmText="Leave"
            cancelText="Cancel"
            variantConfirm="danger"
          />
        )}
      </ReactRouterPrompt>
    </div>
  );
};

export default ChatbotDetails;
