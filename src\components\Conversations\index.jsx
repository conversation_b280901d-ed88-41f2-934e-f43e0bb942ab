import { useEffect, useState } from "react";
import { DateTime } from "luxon";

import useDante<PERSON>pi from "@/hooks/useDanteApi";
import * as conversationsService from "@/services/conversations.service";
import { useChatbotStore } from "@/stores/chatbot/chatbotStore";
import { useConversationStore } from "@/stores/conversation/conversationStore";

import DButton from "../Global/DButton";
import DInput from "../Global/DInput/DInput";
import AddIcon from "../Global/Icons/AddIcon";
import CloseIcon from "../Global/Icons/CloseIcon";
import SearchIcon from "../Global/Icons/SearchIcon";


import ConversationItem from "./ConversationItem";
import ModalShareConversation from "./ModalShareConversation";
import { useParams } from "react-router-dom";
import DButtonIcon from "../Global/DButtonIcon";
import clsx from "clsx";
import { Transition } from "@headlessui/react";
import DLoading from "../DLoading";
import DConfirmationModal from "../Global/DConfirmationModal";
import useToast from "@/hooks/useToast";
import useIsAboveBreakpoint from "@/helpers/useIsAboveBreakpoint";
import QuietMoonIcon from "../Global/Icons/QuietMoonIcon";

const GROUP_TYPES = [
  { label: "Today", value: "today" },
  { label: "Yesterday", value: "yesterday" },
  { label: "Last 7 days", value: "last7days" },
  { label: "Last 30 days", value: "last30days" },
  { label: "Older", value: "olderThan30days" },
];

/**
 * Conversations component displays a list of conversations, allows renaming, deleting, and creating new conversations.
 *
 * @param {Object} props - The properties passed to the Conversations component.
 * @param {Function} props.onClose - Callback function triggered when the user closes the modal.
 *
 * @returns {JSX.Element} The rendered Conversations component.
 */
const Conversations = ({ onClose, forceRefetch, shouldRefetch }) => {
  const params = useParams();
  const selectedChatbot = useChatbotStore((state) => state.selectedChatbot);
  const resetCurrentConversation = useConversationStore(
    (state) => state.resetCurrentConversation
  );
  const setCurrentConversation = useConversationStore(
    (state) => state.setCurrentConversation
  );
  const isAboveSm = useIsAboveBreakpoint("sm");

  const { addSuccessToast } = useToast();

  const [selectedConversations, setSelectedConversations] = useState([]);
  const [groupedConversations, setGroupedConversations] = useState({});
  const [showSearch, setShowSearch] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isShareModalOpen, setIsShareModalOpen] = useState(false);
  const [conversationToDelete, setConversationToDelete] = useState(null);
  const [conversationToShare, setConversationToShare] = useState(null);
  const [search, setSearch] = useState("");
  const [isDeleting, setIsDeleting] = useState(false);
  const {
    data: conversations,
    isLoading: conversationsLoading,
    refetch,
  } = useDanteApi(
    conversationsService.getConversationsByKbId,
    [],
    {},
    params.id
  );

  useEffect(() => {
    if (conversations?.results) {
      setSelectedConversations(conversations.results);
      groupConversationsByDate(conversations.results);
    }
  }, [conversations]);

  const handleSearch = async (query) => {
    try {
      const { data: res } = await conversationsService.getSearchConversations({
        query,
        kb_id: selectedChatbot?.knowledge_base?.id,
      });
      setSelectedConversations(res);
      groupConversationsByDate(res);
    } catch (error) {
      console.error("Failed to search conversations:", error);
    }
  };

  const groupConversationsByDate = (conversations) => {
    const today = DateTime.local().startOf("day");
    const yesterday = today.minus({ days: 1 });
    const last7Days = today.minus({ days: 7 });
    const last30Days = today.minus({ days: 30 });

    const grouped = conversations.reduce(
      (groups, obj) => {
        const dateCreated = DateTime.fromISO(obj.date_created).startOf("day");

        if (dateCreated.equals(today)) {
          groups.today.push(obj);
        } else if (dateCreated.equals(yesterday)) {
          groups.yesterday.push(obj);
        } else if (dateCreated >= last7Days) {
          groups.last7days.push(obj);
        } else if (dateCreated >= last30Days) {
          groups.last30days.push(obj);
        } else {
          groups.olderThan30days.push(obj);
        }

        return groups;
      },
      {
        today: [],
        yesterday: [],
        last7days: [],
        last30days: [],
        olderThan30days: [],
      }
    );

    setGroupedConversations(grouped);
  };

  /**
   * Handles renaming of a conversation.
   *
   * @param {Object} params - The rename parameters.
   * @param {string} params.id - The ID of the conversation to rename.
   * @param {string} params.name - The new name for the conversation.
   */
  const handleRename = async ({ id, name }) => {
    try {
      await conversationsService.patchRenameConversation(id, name);

      const updatedConversations = selectedConversations.map((conversation) => {
        if (conversation.id === id) {
          return { ...conversation, name };
        }
        return conversation;
      });
      setSelectedConversations(updatedConversations);
      refetch();
      addSuccessToast({
        message: "Conversation renamed successfully",
      });
    } catch (error) {
      console.error("Failed to rename conversation:", error);
    }
  };

  /**
   * Deletes a conversation after confirmation.
   */
  const handleDeleteConversation = async () => {
    try {
      setIsDeleting(true);
      await conversationsService.deleteConversationById(
        conversationToDelete.id
      );

      const updatedConversations = selectedConversations.filter(
        (conversation) => conversation.id !== conversationToDelete.id
      );

      setSelectedConversations(updatedConversations);
      groupConversationsByDate(updatedConversations);

      addSuccessToast({
        message: "Conversation deleted successfully",
      });
      setIsDeleteModalOpen(false);
      // refetch();
    } catch (error) {
      console.error("Failed to delete conversation:", error);
    } finally {
      setIsDeleting(false);
    }
  };

  /**
   * Triggers the delete modal for a specific conversation.
   *
   * @param {Object} params - The parameters for the conversation to delete.
   * @param {string} params.id - The ID of the conversation to delete.
   */
  const handleDelete = ({ id }) => {
    setConversationToDelete({ id });
    setIsDeleteModalOpen(true);
  };

  const handleShare = ({ id }) => {
    setConversationToShare({ id });
    setIsShareModalOpen(true);
  };

  const handleOpenConversation = (id) => {
    setCurrentConversation({ id, type: "opening" });
    onClose("openConversation");
  };

  useEffect(() => {
    if (shouldRefetch) {
      console.log('🔄 Conversations shouldRefetch triggered, calling forceRefetch');
      refetch();
      // Add a small delay to prevent immediate state changes that might close the sidebar
      setTimeout(() => {
        forceRefetch();
      }, 100);
    }
  }, [shouldRefetch]);

  return (
    <div className="h-full py-size5 px-size3 w-[360px] 3xl:w-[440px] bg-white rounded-size1 overflow-x-hidden scrollbar gap-size3 flex flex-col">
      <header className="flex flex-col gap-size3">
        <div className="flex items-center justify-between">
          <span className="text-xl tracking-tight">Conversations</span>
          <button
            className="dbutton bg-grey-2 rounded-size1 p-size1"
            onClick={() => {
              onClose();
            }}
          >
            <CloseIcon />
          </button>
        </div>
        <div className="flex items-start gap-size1">
          <DButton
            variant="dark"
            size="lg"
            className={`transition-all relative duration-500 ${
              showSearch ? "!size-12" : "h-11"
            }`}
            fullWidth={!showSearch}
            onClick={() => {
              resetCurrentConversation();
              if (!isAboveSm) {
                onClose();
              }
            }}
          >
            <Transition show={showSearch}>
              <span
                className={clsx(
                  "transition duration-200 absolute",
                  "data-[closed]:opacity-0 data-[enter]:delay-100 data-[leave]:delay-0"
                )}
              >
                <AddIcon />
              </span>
            </Transition>
            <Transition show={!showSearch}>
              <span
                className={clsx(
                  "transition duration-200 ml-size1 overflow-hidden absolute",
                  "data-[closed]:opacity-0 data-[enter]:delay-200 data-[leave]:delay-0 data-[closed]:w-0",
                  !showSearch && "w-full",
                  showSearch && "w-0"
                )}
              >
                New conversation
              </span>
            </Transition>
          </DButton>
          {!showSearch && (
            <DButtonIcon
              variant="outlined"
              size="lg"
              className="rounded-size1 p-size1 size-12"
              onClick={() => setShowSearch(!showSearch)}
            >
              <SearchIcon />
            </DButtonIcon>
          )}
          {showSearch && (
            <DInput
              iconPlacement="pre"
              icon={<SearchIcon />}
              placeholder="Search"
              value={search}
              onChange={(e) => {
                setSearch(e.target.value);
                handleSearch(e.target.value);
              }}
              className="h-12"
              hideError
            />
          )}
          {showSearch && (
            <DButtonIcon
              variant="light"
              size="medium"
              className="transition-all duration-300 rounded-size1 p-size1 size-12 "
              onClick={() => setShowSearch(false)}
            >
              <CloseIcon />
            </DButtonIcon>
          )}
        </div>
      </header>
      <div className="flex flex-col gap-size3 h-full overflow-y-auto scrollbar overflow-x-hidden w-full">
        {conversationsLoading && <DLoading show={true} />}
        {!conversationsLoading && (
          <>
            {selectedConversations.length > 0 ? (
              // <div className='h-full overflow-y-auto'>
              GROUP_TYPES.map((group) => (
                <>
                  {groupedConversations[group.value].length > 0 && (
                    <>
                      <h5 className="font-medium">{group.label}</h5>
                      {groupedConversations[group.value].map((conversation) => (
                        <ConversationItem
                          key={conversation.id}
                          conversation={conversation}
                          handleRename={handleRename}
                          handleDelete={handleDelete}
                          handleShare={handleShare}
                          handleOpenConversation={handleOpenConversation}
                        />
                      ))}
                    </>
                  )}
                </>
              ))
            ) : (
              // </div>
              <div className="flex flex-col items-center justify-center w-full h-full">
                <div className="flex flex-col w-full gap-size6 items-center">
                  <div className="flex flex-col items-center gap-size3  bg-grey-1  p-size5 rounded-[40px]">
                    <div className="flex items-center justify-center bg-grey-2  p-size5 rounded-[40px]">
                      <QuietMoonIcon className="w-[64px] h-[64px] " />
                    </div>
                  </div>

                  <div className="w-full flex flex-col items-center">
                    <h3 className="text-xl">
                      {showSearch
                        ? "No conversations found"
                        : "You have been quiet"}
                    </h3>
                    <p className="text-sm font-regular tracking-tight text-center">
                      {showSearch
                        ? "No conversations match your search"
                        : "You have not yet had a conversation with this AI Chatbot."}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>

      <DConfirmationModal
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConversation}
        title="Delete conversation?"
        description="Are you sure you want to delete this conversation? This action is irreversible."
        confirmText="Delete"
        cancelText="Cancel"
        variantConfirm="danger"
        loading={isDeleting}
      />
      {isShareModalOpen && (
        <ModalShareConversation
          open={isShareModalOpen}
          onClose={(e) => {
            console.log('🔄 Share modal closing', e);
            setIsShareModalOpen(false);
            setConversationToShare(null);
          }}
          conversation_id={conversationToShare?.id}
        />
      )}
    </div>
  );
};

export default Conversations;
