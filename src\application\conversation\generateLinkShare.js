import { postConversationShareLink } from '@/services/conversations.service';

const generateLinkShare = async (conversation_id) => {
  try {
    console.log('Calling postConversationShareLink with conversation_id:', conversation_id);
    const response = await postConversationShareLink(conversation_id);
    console.log('API response:', response);

    const pathName = `/share/?id=${response.data.id}`;
    console.log('Generated pathname:', pathName);
    return pathName;
  } catch (error) {
    console.error('Error in generateLinkShare:', error);
    console.error('Error response:', error.response);
    throw error;
  }
};

export default generateLinkShare;
