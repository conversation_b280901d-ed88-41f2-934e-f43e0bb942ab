import { useEffect, useMemo, useState } from 'react';

import generateLinkShare from '@/application/conversation/generateLinkShare';
import transformLinkUri from '@/helpers/transformLinkUri';
import transformMessageToBubbleMessage from '@/helpers/transformMessageToBubbleMessage';
import useDante<PERSON><PERSON> from '@/hooks/useDanteApi';
import { getMessagesByConversationId } from '@/services/message.service';

import ChatListMessages from '../Chatbot/ChatListMessages';
import DButton from '../Global/DButton';
import DModal from '../Global/DModal';
import CopyIcon from '../Global/Icons/CopyIcon';

const ModalShareConversation = ({ open, onClose, conversation_id }) => {
  const memoizedConversationId = useMemo(
    () => conversation_id,
    [conversation_id]
  );
  const [messages, setMessages] = useState([]);
  const { data: messagesRaw, isLoading: messagesLoading } = useDante<PERSON><PERSON>(
    getMessagesByConversationId,
    [],
    {},
    memoizedConversationId
  );

  const [loadingLink, setLoadingLink] = useState(false);

  const handleCopyLink = async () => {
    try {
      setLoadingLink(true);
      console.log('Starting share process for conversation:', conversation_id);
      const linkPathname = await generateLinkShare(conversation_id);
      console.log('Generated link pathname:', linkPathname);

      const link = `${window.location.origin}${linkPathname}`;
      console.log('Full link to copy:', link);

      navigator.clipboard.writeText(link);
      console.log('Link copied to clipboard successfully');
    } catch (error) {
      console.error('Error in handleCopyLink:', error);
      console.error('Error details:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
    } finally {
      setLoadingLink(false);
    }
  };

  useEffect(() => {
    if (messagesRaw?.results) {
      const processedMessages = [];

      messagesRaw.results.forEach((message) => {
        const transformedMessages = transformMessageToBubbleMessage(message);
        processedMessages.push(...transformedMessages);
      });

      setMessages(processedMessages);
    }
  }, [messagesRaw]);

  return (
    <DModal
      open={open}
      onClose={onClose}
      title="Share your chat with a link"
      subtitle="Messages sent after creating the link won't be shared. The link allows anyone to view this chat."
      footer={
        <DButton
          variant="dark"
          fullWidth
          onClick={handleCopyLink}
          loading={loadingLink}
        >
          <CopyIcon />
          Copy link
        </DButton>
      }
    >
      <div className='h-[500px] overflow-y-auto'>
        <ChatListMessages
          transformLinkUri={transformLinkUri}
          messages={messages}
          readonly
          hideFooter
        />
      </div>
    </DModal>
  );
};

export default ModalShareConversation;
