import React, { useEffect, useState } from 'react';
import useIsAboveBreakpoint from '@/helpers/useIsAboveBreakpoint';
import { Dialog, DialogPanel, Transition } from '@headlessui/react';
import useLayoutStore from '@/stores/layout/layoutStore';
import DButton from '@/components/Global/DButton';
import clsx from 'clsx';

/**
 * This layout is used on pages with two areas: `children` and `RightSidebar`.
 * On screens smaller than the `sm` breakpoint, the `RightSidebar` is hidden and
 * only appears when the `handleRightSidebar` function is triggered.
 * On screens larger than `sm`, the `RightSidebar` is displayed alongside the `children`,
 * with the `children` area always taking up the maximum available space.
 * The width of the `RightSidebar` is not standardized by this layout.
 */

const LayoutRightSidebar = ({ children, RightSidebar, showSidebar = true }) => {
  const previewBubblePage = useLayoutStore((state) => state.previewBubblePage);
  const setPreviewBubblePage = useLayoutStore(
    (state) => state.setPreviewBubblePage
  );
  const setIsInPreviewBubblePage = useLayoutStore(
    (state) => state.setIsInPreviewBubblePage
  );

  const isAboveSm = useIsAboveBreakpoint('sm');
  const isAbovePreview = useIsAboveBreakpoint('preview');

  const [showRightSidebarMobile, setShowRightSidebarMobile] = useState(false);

  const handleRightSidebar = () => {
    setShowRightSidebarMobile(!showRightSidebarMobile);
  };

  useEffect(() => {
    if (!isAbovePreview && previewBubblePage.inPage) {
      setPreviewBubblePage({ inPage: true, isOpen: true });
    }
  }, [isAbovePreview, previewBubblePage.inPage]);

  useEffect(() => {
    return () => {
      setIsInPreviewBubblePage(false);
    };
  }, []);

  if (
    (previewBubblePage.inPage && isAbovePreview) ||
    (!previewBubblePage.inPage && isAboveSm)
  ) {
    return (
      <main
        className={clsx(
          'layout-right-sidebar flex flex-row gap-size3',
          showSidebar ? 'h-full' : 'h-full',
          'w-full'
        )}
      >
        <div
          className={clsx(
            'grow flex flex-col',
            showSidebar
              ? 'max-w-[calc(100%-376px)] 3xl:max-w-[calc(100%-456px)]'
              : 'w-full'
          )}
        >
          {typeof children === 'function'
            ? children(handleRightSidebar)
            : children}
        </div>
        <Transition show={showSidebar}>
          <aside className="transition duration-200 grow-0">
            {typeof RightSidebar === 'function'
              ? RightSidebar(handleRightSidebar)
              : RightSidebar}
          </aside>
        </Transition>
      </main>
    );
  }

  return (
    <main className={clsx('h-full gap-size5', 'w-full', '3xl:max-w-[800px] 3xl:mx-auto')}>
      <div className="h-full flex flex-col gap-size2">
        <Transition
          show={
            (previewBubblePage.inPage && !showRightSidebarMobile) ||
            !previewBubblePage.inPage
          }
        >
          <div
            className={clsx(
              previewBubblePage.inPage ? 'h-[calc(100%-48px)]' : 'h-full',
              'flex flex-col',
              'transition duration-200 data-[enter]:delay-200 data-[closed]:opacity-0 data-[closed]:-translate-x-1'
            )}
          >
            {typeof children === 'function'
              ? children(handleRightSidebar)
              : children}
          </div>
        </Transition>

        <Transition show={previewBubblePage.inPage && showRightSidebarMobile}>
          <div
            className={clsx(
              previewBubblePage.inPage ? 'h-[calc(100%-48px)]' : '',
              'flex flex-col items-center',
              'transition duration-200 data-[enter]:delay-200 data-[closed]:opacity-0 data-[closed]:-translate-x-1'
            )}
          >
            {typeof RightSidebar === 'function'
              ? RightSidebar(handleRightSidebar)
              : RightSidebar}
          </div>
        </Transition>

        {previewBubblePage.inPage && (
          <div className="w-full flex justify-center">
            <DButton
              onClick={handleRightSidebar}
              variant="outlined"
              fullWidth
              className="max-w-sm"
            >
              {showRightSidebarMobile ? 'Back to editing' : 'Preview chatbot'}
            </DButton>
          </div>
        )}
      </div>
      {(!previewBubblePage.inPage || !isAboveSm) && (
        <Dialog
          open={showRightSidebarMobile}
          onClose={() => setShowRightSidebarMobile(false)}
          className="relative z-10"
        >
          <aside className="fixed inset-0 overflow-hidden">
            <DialogPanel
              transition
              className={clsx(
                'pointer-events-auto relative w-screen transform transition duration-150 ease-in-out data-[closed]:opacity-0 sm:duration-200',
                'flex flex-col items-center h-full bg-gray-100 p-size2 gap-size2 justify-between'
              )}
            >
              <div
                className={clsx(previewBubblePage.inPage ? 'h-full' : 'h-full')}
              >
                {typeof RightSidebar === 'function'
                  ? RightSidebar(handleRightSidebar)
                  : RightSidebar}
              </div>
              {previewBubblePage.inPage && (
                <div className="w-full flex justify-center">
                  <DButton
                    onClick={handleRightSidebar}
                    variant="outlined"
                    fullWidth
                    className="max-w-sm"
                  >
                    Back to editing
                  </DButton>
                </div>
              )}
            </DialogPanel>
          </aside>
        </Dialog>
      )}
    </main>
  );
};

export default LayoutRightSidebar;
